import 'package:condogaia/app/widgets/dropdown_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

class BillLayoutColumn extends StatelessWidget {
  final String title;
  final List<String> dropdownList;
  final String selected;
  final Function(int) onChanged;
  final VoidCallback onViewPressed;

  const BillLayoutColumn({
    super.key,
    required this.title,
    required this.dropdownList,
    required this.selected,
    required this.onChanged,
    required this.onViewPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () => _showDialog(context),
          child: Observer(
            builder: (_) => CustomDropdownButton(
              list: dropdownList,
              selected: selected,
              function: onChanged,
            ),
          ),
        ),
        const SizedBox(height: 8),
        TextButton(
          onPressed: onViewPressed,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset('assets/management/bill/bill_icon.png'),
              const Text(' Ver '),
            ],
          ),
        ),
      ],
    );
  }

  void _showDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 50),
        child: SizedBox(
          height: 300,
          child: ListView.builder(
            itemCount: 1, // pode ser dynamic depois
            itemBuilder: (_, index) => ListTile(
              onTap: () => Navigator.of(context).pop(),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(15)),
              ),
              title: Text(title),
            ),
          ),
        ),
      ),
    );
  }
}
