import 'package:condogaia/app/modules/management/domain/repositories/condominio_repository.dart';

class SendSheetUsecase {
  final CondominioRepository _condominioRepository;

  SendSheetUsecase(this._condominioRepository);

  Future<String> sendSheet(String condId, String url, String type) async {
    return await _condominioRepository.sendSheet(condId, url, type);
  }

  Future<Map<String, dynamic>> downloadImportTemplate(String condId) async {
    return await _condominioRepository.downloadImportTemplate(condId);
  }
}
