import 'dart:html' as html;
import 'dart:typed_data';

import 'pdf_downloader_interface.dart';

class PdfDownloader<PERSON>eb implements PdfDownloader {
  @override
  Future<void> downloadPdf(Uint8List bytes, String name) async {
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);

    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', '$name.pdf');
    anchor.click();

    html.Url.revokeObjectUrl(url);
  }
}

PdfDownloader getPdfDownloader() => PdfDownloaderWeb();
