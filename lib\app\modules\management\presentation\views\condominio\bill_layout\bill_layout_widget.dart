import 'package:condogaia/app/constants/colors.dart';
import 'package:condogaia/app/modules/home/<USER>/controllers/home_controller.dart';
import 'package:condogaia/app/modules/management/presentation/controllers/management/bill_layout/bill_layout_controller.dart';
import 'package:condogaia/app/modules/management/presentation/controllers/management/management_cond_controller.dart';
import 'package:condogaia/app/modules/management/presentation/utils/condominio_navigation.dart';
import 'package:condogaia/app/modules/management/presentation/views/condominio/bill_layout/bill_layout_column.dart';
import 'package:condogaia/app/modules/shared/presentation/controllers/htmls_controller.dart';
import 'package:condogaia/app/utils/show_pdf.dart';
import 'package:condogaia/app/widgets/white_button.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class BillLayoutWidget extends StatefulWidget {
  final int? index;
  const BillLayoutWidget({super.key, this.index});

  @override
  State<BillLayoutWidget> createState() => _BillLayoutWidgetState();
}

class _BillLayoutWidgetState extends State<BillLayoutWidget> {
  final managementCondController = GetIt.I<ManagementCondController>();
  final billLayoutController = GetIt.I<BillLayoutController>();
  final homeController = GetIt.I<HomeController>();
  final htmlsController = GetIt.I<HtmlsController>();

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: billLayoutController
          .getBillLayout(homeController.selectedCondominio.condId),
      builder: (context, snapshot) {
        if (billLayoutController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final condId = homeController.selectedCondominio.condId;

        return Column(
          children: [
            InkWell(
              onTap: () => managementCondController
                      .stepDownListCondominio[widget.index!] =
                  !managementCondController
                      .stepDownListCondominio[widget.index!],
              child: Row(
                children: [
                  Expanded(
                    flex: 8,
                    child: Text(
                      ' ${listNames[widget.index!]}',
                      style: const TextStyle(
                        fontSize: 15,
                        color: ColorsUtils.darkBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Expanded(
                    flex: 1,
                    child: Image(
                      image: AssetImage('assets/up_arrow.png'),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                BillLayoutColumn(
                  title: 'Mensal',
                  dropdownList: billLayoutController.monthlyList,
                  selected: billLayoutController.monthlySelected,
                  onChanged: billLayoutController.selectMonthly,
                  onViewPressed: () async {
                    await htmlsController.getHtml(
                      condId: condId,
                      name: "fake",
                      templateId: billLayoutController.monthlySelected,
                    );
                    if (!context.mounted) return;
                    await showPdf(
                      context,
                      htmlsController.fake,
                      "Mensal",
                      name: htmlsController.fake["pdfName"],
                    );
                  },
                ),
                BillLayoutColumn(
                  title: 'Acordo',
                  dropdownList: billLayoutController.agreementList,
                  selected: billLayoutController.agreementSelected,
                  onChanged: billLayoutController.selectAgreement,
                  onViewPressed: () async {
                    await htmlsController.getHtml(
                      condId: condId,
                      name: "fake",
                      templateId: billLayoutController.agreementSelected,
                    );
                    if (!context.mounted) return;
                    await showPdf(
                      context,
                      htmlsController.fake,
                      "Acordo",
                      name: htmlsController.fake["pdfName"],
                    );
                  },
                ),
                BillLayoutColumn(
                  title: 'Avulso',
                  dropdownList: billLayoutController.separeteList,
                  selected: billLayoutController.separeteSelected,
                  onChanged: billLayoutController.selectSeparete,
                  onViewPressed: () async {
                    await htmlsController.getHtml(
                      condId: condId,
                      name: "fake",
                      templateId: billLayoutController.separeteSelected,
                    );
                    if (!context.mounted) return;
                    await showPdf(
                      context,
                      htmlsController.fake,
                      "Avulso",
                      name: htmlsController.fake["pdfName"],
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
            InkWell(
              onTap: () async =>
                  await billLayoutController.saveBillLayout(condId),
              child: const WhiteButton(
                text: 'Salvar',
                height: 50,
                width: 150,
                color: ColorsUtils.darkBlue,
                fontSize: 18,
                textColor: ColorsUtils.white1,
              ),
            ),
            const SizedBox(height: 15),
          ],
        );
      },
    );
  }
}
