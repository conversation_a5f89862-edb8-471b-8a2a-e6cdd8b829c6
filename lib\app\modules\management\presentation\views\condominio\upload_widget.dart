import 'package:condogaia/app/constants/colors.dart';
import 'package:condogaia/app/modules/home/<USER>/controllers/home_controller.dart';
import 'package:condogaia/app/modules/management/presentation/controllers/management/condominio/cond_information_controller.dart';
import 'package:condogaia/app/modules/management/presentation/controllers/management/management_cond_controller.dart';
import 'package:condogaia/app/modules/management/presentation/utils/condominio_navigation.dart';
import 'package:condogaia/app/utils/snack_bar.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class UploadWidget extends StatefulWidget {
  final int index;
  const UploadWidget({super.key, required this.index});

  @override
  State<UploadWidget> createState() => _UploadWidgetState();
}

class _UploadWidgetState extends State<UploadWidget> {
  var managementCondController = GetIt.I.get<ManagementCondController>();
  var condController = GetIt.I.get<CondInformationController>();
  var homeController = GetIt.I.get<HomeController>();

  TextEditingController url1 = TextEditingController();
  TextEditingController url2 = TextEditingController();
  TextEditingController url3 = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: () => managementCondController
                  .stepDownListCondominio[widget.index] =
              !managementCondController.stepDownListCondominio[widget.index],
          child: Row(
            children: [
              Expanded(
                flex: 8,
                child: Text(
                  ' ${listNames[widget.index]}',
                  style: const TextStyle(
                      fontSize: 15,
                      color: ColorsUtils.darkBlue,
                      fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 1,
                child: Image.asset('assets/up_arrow.png'),
              )
            ],
          ),
        ),
        SizedBox(height: 20),
        InkWell(
          onTap: () async {
            final result = await condController.downloadImportTemplate(
                homeController.selectedCondominio.condId);

            if (!context.mounted) return;

           if (result["success"] == true) {
  final templates = result["data"];

  if (templates is List) {
    for (var template in templates) {
      _downloadTemplate(template);
    }
  } else if (templates != null) {
    _downloadTemplate(templates);
  }

  showSnackBar(context, "Templates baixados com sucesso!");
}

            } else {
              showSnackBar(
                  context, result["message"] ?? "Erro ao baixar templates");
            }
          },
          child: Row(
            children: [
              Icon(Icons.download, color: ColorsUtils.darkBlue, size: 20),
              SizedBox(width: 10),
              Text("Baixar Modelos de Importação",
                  style: const TextStyle(
                      fontSize: 15,
                      color: ColorsUtils.darkBlue,
                      fontWeight: FontWeight.bold)),
            ],
          ),
        ),
        SizedBox(height: 20),
        Text(
          ' Moradores Unidade/Bloco',
          style: const TextStyle(
              fontSize: 15,
              color: ColorsUtils.darkBlue,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10),
        TextFormField(
          controller: url1,
          decoration: InputDecoration(
            label: const Text('Link da planilha'),
            suffixIcon: InkWell(
              onTap: () async {
                await condController
                    .sendSheet(homeController.selectedCondominio.condId,
                        url1.text, "unit")
                    .then((value) {
                  if (!context.mounted) return;
                  showSnackBar(context, value);
                });
              },
              child: Image.asset("assets/upload.png"),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        // TODO adicionar quando tiver relatório de acordo
        // SizedBox(height: 10),
        // Text(
        //   ' Indimplência',
        //   style: const TextStyle(
        //       fontSize: 15,
        //       color: ColorsUtils.darkBlue,
        //       fontWeight: FontWeight.bold),
        // ),
        // SizedBox(height: 10),
        // TextFormField(
        //   controller: url2,
        //   decoration: InputDecoration(
        //     label: const Text('Link da planilha'),
        //     suffixIcon: InkWell(
        //       onTap: () async {
        //         await condController
        //             .sendSheet(homeController.selectedCondominio.condId,
        //                 url2.text, "unit")
        //             .then((value) {
        //           showSnackBar(context, value);
        //         });
        //       },
        //       child: Image.asset("assets/upload.png"),
        //     ),
        //     border: OutlineInputBorder(
        //       borderRadius: BorderRadius.circular(12),
        //     ),
        //   ),
        // ),
        SizedBox(height: 10),
        Text(
          ' Acordo',
          style: const TextStyle(
              fontSize: 15,
              color: ColorsUtils.darkBlue,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10),
        TextFormField(
          controller: url3,
          decoration: InputDecoration(
            label: const Text('Link da planilha'),
            suffixIcon: InkWell(
              onTap: () async {
                await condController
                    .sendSheet(homeController.selectedCondominio.condId,
                        url3.text, "unit")
                    .then((value) {
                  if (!context.mounted) return;
                  showSnackBar(context, value);
                });
              },
              child: Image.asset("assets/upload.png"),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  void _downloadTemplate(dynamic template) {
    // Handle template download
    if (template != null) {
      // You can implement the actual download logic here
      // For example, if the template contains a URL or file data
      showSnackBar(context, "Iniciando download do template...");

      // Example: if template has a URL
      if (template is Map && template.containsKey('url')) {
        // Launch URL or trigger download
        // You could use url_launcher package or implement file download
      }

      // Example: if template has file data
      if (template is Map && template.containsKey('fileData')) {
        // Process file data and trigger download
        // You could use the existing PDF download infrastructure
      }
    }
  }
}
