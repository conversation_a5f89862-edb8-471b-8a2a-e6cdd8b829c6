import 'package:condogaia/app/modules/management/domain/entities/condominio/codminio_entity.dart';
import 'package:condogaia/app/modules/management/domain/usecases/condominio/get_cond_usecase.dart';
import 'package:condogaia/app/modules/management/domain/usecases/condominio/send_sheet_usecase.dart';
import 'package:condogaia/app/modules/shared/domain/entities/adress_entity.dart';
import 'package:condogaia/app/modules/shared/domain/usecases/get_cep_usecase.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';

part 'cond_information_controller.g.dart';

class CondInformationController = CondInformationControllerBase
    with _$CondInformationController;

abstract class CondInformationControllerBase with Store {
  var getCondUseCase = GetIt.I.get<GetCondUsecase>();
  var getAdressUseCase = GetIt.I.get<GetAddressUseCase>();
  var sendSheetUseCase = GetIt.I.get<SendSheetUsecase>();

  @observable
  String cep = '';

  @observable
  String adressInput = '';

  @observable
  String neibourghoodInput = '';

  @observable
  String cityInput = '';

  @observable
  String stateInput = '';

  @observable
  int condType = 1;

  @observable
  int hasBloc = 1;

  @observable
  CondominioEntity condominioModel = CondominioEntity(
    condId: '',
    unionId: '',
    condRegister: '',
    condName: '',
    celNumber: '',
    telNumber: '',
    email: '',
    webSite: '',
    residencialOrComecial: '',
    hasUnities: false,
    updateDate: DateTime.now(),
    responsibleDocument: '',
    logo: '',
    cnae: '',
    softDiscripton: '',
    active: false,
    addressEntity: AddressEntity(
        zipCode: '',
        street: '',
        neighborhood: '',
        city: '',
        state: '',
        number: '',
        complement: ''),
    typeCompany: '',
    logoId: '',
    hasGarantor: false,
  );

  @action
  Future getCond(String condId) async {
    var result = await getCondUseCase.getCondominio(condId);

    if (result != null) {
      cep = result.addressEntity.zipCode;
      getCep();
    }

    if (result != null) {
      condominioModel = result;
      if (result.hasUnities) {
        hasBloc == 1;
      } else {
        hasBloc = 0;
      }

      if (result.residencialOrComecial == 'Residencial') {
        condType == 1;
      } else {
        condType = 0;
      }
    } else {
      return condominioModel;
    }
  }

  @action
  Future<void> getCep() async {
    if (cep.length >= 8) {
      final adress = await getAdressUseCase.getAddress(cep);
      if (adress != null) {
        adressInput = adress.street;
        neibourghoodInput = adress.neighborhood;
        cityInput = adress.city;
        stateInput = adress.state;

        condominioModel.addressEntity.street = adress.street;
        condominioModel.addressEntity.city = adress.city;
        condominioModel.addressEntity.neighborhood = adress.neighborhood;
        condominioModel.addressEntity.state = adress.state;
        condominioModel.addressEntity.zipCode = adress.zipCode;
      }
    }
  }

  @action
  Future<String> sendSheet(String condId, String url, String type) async {
    return await sendSheetUseCase.sendSheet(condId, url, type);
  }

  @action
  Future<Map<String, dynamic>> downloadImportTemplate(String condId) async {
    return await sendSheetUseCase.downloadImportTemplate(condId);
  }
}
