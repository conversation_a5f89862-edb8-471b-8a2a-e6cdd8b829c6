import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:condogaia/app/constants/api_url.dart';
import 'package:condogaia/app/modules/management/data/models/condominio_models/bank_account_model.dart';
import 'package:condogaia/app/modules/management/data/models/condominio_models/bill_layout_model.dart';
import 'package:condogaia/app/modules/management/data/models/condominio_models/bill_text_model.dart';
import 'package:condogaia/app/modules/management/data/models/condominio_models/cond_model.dart';
import 'package:condogaia/app/modules/management/data/models/condominio_models/financial_model.dart';
import 'package:condogaia/app/modules/management/data/remote_data_source/condominio/cond_remote_data_source.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bank_account_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bill_layout_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bill_text_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/codminio_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/financial_entity.dart';
import 'package:condogaia/app/modules/shared/data/models/user_model.dart';
import 'package:condogaia/app/modules/shared/domain/entities/user_entity.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';

class CondRemoteDataSourceImpl implements CondRemoteDataSource {
  var auth = GetIt.I.get<FirebaseAuth>();
  var firestore = GetIt.I.get<FirebaseFirestore>();

  late User? user = auth.currentUser!;

  @override
  Future<bool> saveBankAccount(
      String condId, BankAccountEntity bankAccountEntity) async {
    try {
      if (bankAccountEntity.accountId.isEmpty) {
        String accountId = const Uuid().v1();

        final bankAccountsDocs = await firestore
            .collection('condominios')
            .doc(condId)
            .collection('bank_accounts')
            .get();

        if (bankAccountsDocs.docs.isEmpty) {
          bankAccountEntity.main = true;
        }

        await firestore
            .collection('condominios')
            .doc(condId)
            .collection('bank_accounts')
            .doc(accountId)
            .set(
              BankAccountModel(
                accountId: accountId,
                condId: bankAccountEntity.condId,
                accountType: bankAccountEntity.accountType,
                bankName: bankAccountEntity.bankName,
                accountNumber: bankAccountEntity.accountNumber,
                agencyNumber: bankAccountEntity.agencyNumber,
                key: bankAccountEntity.key,
                pixType: bankAccountEntity.pixType,
                main: bankAccountEntity.main,
              ).toDocument(),
            );
      } else {
        await firestore
            .collection('condominios')
            .doc(bankAccountEntity.condId)
            .collection('bank_accounts')
            .doc(bankAccountEntity.accountId)
            .set(
              BankAccountModel(
                accountId: bankAccountEntity.accountId,
                condId: bankAccountEntity.condId,
                accountType: bankAccountEntity.accountType,
                bankName: bankAccountEntity.bankName,
                accountNumber: bankAccountEntity.accountNumber,
                agencyNumber: bankAccountEntity.agencyNumber,
                key: bankAccountEntity.key,
                pixType: bankAccountEntity.pixType,
                main: bankAccountEntity.main,
              ).toDocument(),
            );
      }
      return true;
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return false;
    }
  }

  @override
  Future<bool> saveBillLayout(
      String condId, BillLayoutEntity billLayoutEntity) async {
    try {
      await firestore.collection('condominios').doc(condId).set(
            BillLayoutModel(
                    billType: billLayoutEntity.billType,
                    agreementType: billLayoutEntity.agreementType,
                    oneChargeOffType: billLayoutEntity.oneChargeOffType)
                .toDocument(),
            SetOptions(merge: true),
          );
      return true;
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return false;
    }
  }

  @override
  Future<bool> saveBillTexts(
      String condId, BillTextEntity billTextEntity) async {
    try {
      if (billTextEntity.textId.isEmpty) {
        String textId = const Uuid().v1();

        await firestore
            .collection('condominios')
            .doc(billTextEntity.condId)
            .collection('texts')
            .doc(textId)
            .set(BillTextModel(
                    textId: textId,
                    condId: billTextEntity.condId,
                    billType: billTextEntity.billType,
                    textContent: billTextEntity.textContent,
                    textType: billTextEntity.textContent,
                    page: billTextEntity.page)
                .toDocument());
      } else {
        await firestore
            .collection('condominios')
            .doc(billTextEntity.condId)
            .collection('texts')
            .doc(billTextEntity.textId)
            .set(BillTextModel(
                    textId: billTextEntity.textId,
                    condId: billTextEntity.condId,
                    billType: billTextEntity.billType,
                    textContent: billTextEntity.textContent,
                    textType: billTextEntity.textContent,
                    page: billTextEntity.page)
                .toDocument());
      }
      return true;
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return false;
    }
  }

  @override
  Future<bool> saveFinancial(
      String condId, FinancialEntity financialEntity) async {
    try {
      await firestore.collection('condominios').doc(condId).set(
          FinancialModel(
            fees: financialEntity.fees,
            fine: financialEntity.fine,
            maturity: financialEntity.maturity,
            discount: financialEntity.discount,
            chargeType: financialEntity.chargeType,
            divisionQuota: financialEntity.divisionQuota,
            expenseIncomeRef: financialEntity.expenseIncomeRef,
            offDays: financialEntity.offDays,
            houseTypes: financialEntity.houseTypes,
            meterPrice: financialEntity.meterPrice,
            fraction: financialEntity.fraction,
            reserveAmmount: financialEntity.reserveAmmount,
          ).toDocument(),
          SetOptions(merge: true));

      return true;
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return false;
    }
  }

  @override
  Stream<List<BankAccountEntity>> getBankAccount(String condId) {
    return firestore
        .collection('condominios')
        .doc(condId)
        .collection('bank_accounts')
        .snapshots()
        .map((query) => query.docs
            .map((doc) => BankAccountModel.fromSnapshot(doc))
            .toList());
  }

  @override
  Stream<List<BillTextEntity>> getBillTexts(String condId) {
    return firestore
        .collection('condominios')
        .doc(condId)
        .collection('texts')
        .snapshots()
        .map((query) =>
            query.docs.map((doc) => BillTextModel.fromSnapshot(doc)).toList());
  }

  @override
  Future<CondominioEntity?> getCondominio(String condId) async {
    try {
      final condominio = await firestore
          .collection('sindicos')
          .doc(user!.uid)
          .collection('condominios')
          .doc(condId)
          .get();

      return CondominioModel.fromSnapshot(condominio);
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return null;
    }
  }

  @override
  Stream<List<UserEntity>> getUsers(String condId) {
    return firestore
        .collection('users')
        .where('condId', isEqualTo: condId)
        .snapshots()
        .map((query) =>
            query.docs.map((doc) => UserModel.fromSnapshot(doc)).toList());
  }

  @override
  Future<BillLayoutEntity?> getBillLayout(String condId) async {
    try {
      final condominio =
          await firestore.collection('condominios').doc(condId).get();

      return BillLayoutModel.fromSnapshot(condominio);
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return null;
    }
  }

  @override
  Future<FinancialEntity?> getFinancial(String condId) async {
    try {
      final condominio =
          await firestore.collection('condominios').doc(condId).get();

      return FinancialModel.fromSnapshot(condominio);
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return null;
    }
  }

  @override
  Future<bool> deleteBankAccount(String bankAccountId, String condId) async {
    try {
      final accountDoc = await firestore
          .collection('condominios')
          .doc(condId)
          .collection('bank_accounts')
          .doc(bankAccountId)
          .get();

      if (accountDoc["main"] == true) {
        final firsAccountDoc = await firestore
            .collection('condominios')
            .doc(condId)
            .collection('bank_accounts')
            .where("accountId", isNotEqualTo: null)
            .get();

        await firestore
            .collection('condominios')
            .doc(condId)
            .collection('bank_accounts')
            .doc(firsAccountDoc.docs.first.id)
            .update({"main": true});
      }

      await firestore
          .collection('condominios')
          .doc(condId)
          .collection('bank_accounts')
          .doc(bankAccountId)
          .delete();

      return true;
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return false;
    }
  }

  @override
  Future<bool> saveHouseType(String condId, Map<String, int> houseMap) async {
    try {
      final houseDoc =
          await firestore.collection("condominios").doc(condId).get();

      if (houseDoc.exists) {
        FinancialModel financialModel = FinancialModel.fromSnapshot(houseDoc);
        financialModel.houseTypes.addAll(houseMap);
        await firestore
            .collection("condominios")
            .doc(condId)
            .set(financialModel.toDocument(), SetOptions(merge: true));
      }
      return true;
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return false;
    }
  }

  @override
  Future<bool> updateHouseType(
      String condId, Map<String, int> newHouseMap) async {
    try {
      final docSnapshot =
          await firestore.collection("condominios").doc(condId).get();
      final Map<String, dynamic>? oldHouseTypes =
          docSnapshot.data()?['houseTypes'] as Map<String, dynamic>?;

      if (oldHouseTypes != null) {
        Set<String> removedTypes = oldHouseTypes.keys
            .where((key) => !newHouseMap.containsKey(key))
            .toSet();

        Set<String> changedValueTypes = oldHouseTypes.keys
            .where((key) =>
                newHouseMap.containsKey(key) &&
                newHouseMap[key] != oldHouseTypes[key])
            .toSet();

        // Atualizar unidades
        if (removedTypes.isNotEmpty) {
          final unitsSnapshot = await firestore
              .collection("condominios")
              .doc(condId)
              .collection("units")
              .get();

          for (var doc in unitsSnapshot.docs) {
            final String currentType = doc.data()['houseType'] ?? '';

            if (removedTypes.contains(currentType)) {
              // Se o tipo foi removido, deixar vazio
              await doc.reference.update({'houseType': ''});
            }
          }
        }

        // Atualizar composições
        if (removedTypes.isNotEmpty || changedValueTypes.isNotEmpty) {
          final payersSnapshot = await firestore
              .collection("condominios")
              .doc(condId)
              .collection("payers")
              .get();

          for (var payerDoc in payersSnapshot.docs) {
            final compositionsSnapshot =
                await payerDoc.reference.collection("compositions").get();

            for (var compDoc in compositionsSnapshot.docs) {
              final Map<String, dynamic> data = compDoc.data();

              if (data["chargeName"] != "cota") {
                continue;
              }

              // Verificar cada campo que pode conter um valor de houseType
              for (var entry in data.entries) {
                if (entry.value is int || entry.value is double) {
                  for (var removedType in removedTypes) {
                    if (oldHouseTypes[removedType] == entry.value) {
                      await compDoc.reference.update({entry.key: 0});
                    }
                  }

                  // Verificar se este valor corresponde a algum tipo com valor alterado
                  for (var changedType in changedValueTypes) {
                    if (oldHouseTypes[changedType] == entry.value) {
                      await compDoc.reference
                          .update({entry.key: newHouseMap[changedType]});
                    }
                  }
                }
              }
            }
          }
        }
      }

      await firestore.collection("condominios").doc(condId).update({
        "houseTypes": newHouseMap,
      });

      return true;
    } catch (e, s) {
      log("Erro ao atualizar tipos de casa: ${e.toString()}",
          name: "HOUSE_TYPE");
      log(s.toString());
      return false;
    }
  }

  @override
  Future<bool> setMainBankAccount(String condId, String bankAccountId) async {
    try {
      final bankAccountsRef = firestore
          .collection("condominios")
          .doc(condId)
          .collection("bank_accounts");

      final olderMainAccounts =
          await bankAccountsRef.where("main", isEqualTo: true).get();

      for (final doc in olderMainAccounts.docs) {
        await doc.reference.update({"main": false});
      }

      await bankAccountsRef.doc(bankAccountId).update({"main": true});

      return true;
    } catch (e, s) {
      log("Erro ao definir conta principal: $e", name: "CONTAS");
      log("StackTrace: $s", name: "CONTAS");
      return false;
    }
  }

  @override
  Future<String> sendSheet(String condId, String url, String type) async {
    try {
      final respone = await http.get(
        Uri.parse(url),
      );

      if (respone.statusCode == 200) {
        final awsResponse = await http.post(
          Uri.parse('$awsUrl/import'),
          headers: {"x-cid": condId},
          body: {"url": url, "type": type},
        );
        print(awsResponse.body);
        if (awsResponse.statusCode == 200) {
          return "Planilha enviada";
        } else {
          return "Desculpe, houve um erro interno";
        }
      } else {
        return "A planilha precisa ser pública para fazer a importação";
      }
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return "Error ao buscar planilha";
    }
  }

  @override
  Future<Map<String, dynamic>> downloadImportTemplate(String condId) async {
    try {
      final response = await http.get(
        Uri.parse('$awsUrl/download-templates'),
        headers: {'Content-Type': 'application/json', "x-cid": condId},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          "success": true,
          "data": responseData,
          "message": "Template baixado com sucesso"
        };
      } else {
        return {
          "success": false,
          "data": null,
          "message": "Erro ao baixar template: ${response.body}"
        };
      }
    } catch (e, s) {
      log(e.toString());
      log(s.toString());
      return {
        "success": false,
        "data": null,
        "message": "Erro ao baixar template"
      };
    }
  }
}
