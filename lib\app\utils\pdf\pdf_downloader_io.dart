import 'dart:io';
import 'dart:typed_data';

import 'package:path_provider/path_provider.dart';

import 'pdf_downloader_interface.dart';

class PdfDownloaderIO implements PdfDownloader {
  @override
  Future<void> downloadPdf(Uint8List bytes, String name) async {
    final dir = await getApplicationDocumentsDirectory();
    final path = '${dir.path}/$name.pdf';
    final file = File(path);
    await file.writeAsBytes(bytes);
    print('PDF salvo em: $path');
  }
}

PdfDownloader getPdfDownloader() => PdfDownloaderIO();
