import 'package:condogaia/app/modules/management/domain/entities/condominio/bank_account_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bill_layout_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bill_text_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/codminio_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/financial_entity.dart';
import 'package:condogaia/app/modules/shared/domain/entities/user_entity.dart';

abstract class CondRemoteDataSource {
  Future<CondominioEntity?> getCondominio(String condId);
  Future<bool> saveFinancial(String condId, FinancialEntity financialEntity);
  Future<bool> saveBillLayout(String condId, BillLayoutEntity billLayoutEntity);
  Future<bool> saveBankAccount(
      String condId, BankAccountEntity bankAccountEntity);
  Stream<List<BankAccountEntity>> getBankAccount(String condId);
  Future<bool> saveBillTexts(String condId, BillTextEntity billTextEntity);
  Stream<List<BillTextEntity>> getBillTexts(String condId);
  Stream<List<UserEntity>> getUsers(String condId);
  Future<FinancialEntity?> getFinancial(String condId);
  Future<BillLayoutEntity?> getBillLayout(String condId);
  Future<bool> deleteBankAccount(String bankAccountId, String condId);
  Future<bool> saveHouseType(String condId, Map<String, int> houseMap);
  Future<bool> updateHouseType(String condId, Map<String, int> houseMap);
  Future<bool> setMainBankAccount(String condId, String bankAccountId);
  Future<String> sendSheet(String condId, String url, String type);
  Future<Map<String, dynamic>> downloadImportTemplate(String condId);
}
