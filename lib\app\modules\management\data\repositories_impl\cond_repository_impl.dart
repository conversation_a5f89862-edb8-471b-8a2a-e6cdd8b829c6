// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:condogaia/app/modules/management/data/remote_data_source/condominio/cond_remote_data_source.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bank_account_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bill_layout_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/bill_text_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/codminio_entity.dart';
import 'package:condogaia/app/modules/management/domain/entities/condominio/financial_entity.dart';
import 'package:condogaia/app/modules/management/domain/repositories/condominio_repository.dart';
import 'package:condogaia/app/modules/shared/domain/entities/user_entity.dart';

class CondominioRepositoryImpl implements CondominioRepository {
  final CondRemoteDataSource _condRemoteDataSource;
  CondominioRepositoryImpl(
    this._condRemoteDataSource,
  );

  @override
  Future<bool> saveBankAccount(
      String condId, BankAccountEntity bankAccountEntity) async {
    return await _condRemoteDataSource.saveBankAccount(
        condId, bankAccountEntity);
  }

  @override
  Future<bool> saveBillLayout(
      String condId, BillLayoutEntity billLayoutEntity) async {
    return await _condRemoteDataSource.saveBillLayout(condId, billLayoutEntity);
  }

  @override
  Future<bool> saveBillTexts(
      String condId, BillTextEntity billTextEntity) async {
    return await _condRemoteDataSource.saveBillTexts(condId, billTextEntity);
  }

  @override
  Future<bool> saveFinancial(
      String condId, FinancialEntity financialEntity) async {
    return await _condRemoteDataSource.saveFinancial(condId, financialEntity);
  }

  @override
  Stream<List<BankAccountEntity>> getBankAccount(String bankAccountId) {
    return _condRemoteDataSource.getBankAccount(bankAccountId);
  }

  @override
  Stream<List<BillTextEntity>> getBillTexts(String billTextsId) {
    return _condRemoteDataSource.getBillTexts(billTextsId);
  }

  @override
  Future<CondominioEntity?> getCondominio(String condId) async {
    return await _condRemoteDataSource.getCondominio(condId);
  }

  @override
  Stream<List<UserEntity>> getUsers(String userId) {
    return _condRemoteDataSource.getUsers(userId);
  }

  @override
  Future<BillLayoutEntity?> getBillLayout(String condId) async {
    return _condRemoteDataSource.getBillLayout(condId);
  }

  @override
  Future<FinancialEntity?> getFinancial(String condId) async {
    return await _condRemoteDataSource.getFinancial(condId);
  }

  @override
  Future<bool> deleteBankAccount(String bankAccountId, String condId) async {
    return await _condRemoteDataSource.deleteBankAccount(bankAccountId, condId);
  }

  @override
  Future<bool> saveHouseType(String condId, Map<String, int> houseMap) async {
    return await _condRemoteDataSource.saveHouseType(condId, houseMap);
  }

  @override
  Future<bool> updateHouseType(String condId, Map<String, int> houseMap) async {
    return await _condRemoteDataSource.updateHouseType(condId, houseMap);
  }

  @override
  Future<bool> setMainBankAccount(String condId, String bankAccountId) async {
    return await _condRemoteDataSource.setMainBankAccount(
        condId, bankAccountId);
  }

  @override
  Future<String> sendSheet(String condId, String url, String type) async {
    return await _condRemoteDataSource.sendSheet(condId, url, type);
  }

  @override
  Future<Map<String, dynamic>> downloadImportTemplate(String condId) async {
    return await _condRemoteDataSource.downloadImportTemplate(condId);
  }
}
