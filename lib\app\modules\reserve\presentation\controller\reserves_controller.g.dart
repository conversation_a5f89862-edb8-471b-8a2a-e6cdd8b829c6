// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reserves_controller.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ReservesController on ReservesControllerBase, Store {
  Computed<List<String>>? _$placesComputed;

  @override
  List<String> get places =>
      (_$placesComputed ??= Computed<List<String>>(() => super.places,
              name: 'ReservesControllerBase.places'))
          .value;
  Computed<String>? _$stringDatesComputed;

  @override
  String get stringDates =>
      (_$stringDatesComputed ??= Computed<String>(() => super.stringDates,
              name: 'ReservesControllerBase.stringDates'))
          .value;

  late final _$deletedPlaceAtom =
      Atom(name: 'ReservesControllerBase.deletedPlace', context: context);

  @override
  bool get deletedPlace {
    _$deletedPlaceAtom.reportRead();
    return super.deletedPlace;
  }

  @override
  set deletedPlace(bool value) {
    _$deletedPlaceAtom.reportWrite(value, super.deletedPlace, () {
      super.deletedPlace = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: 'ReservesControllerBase.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$isLoadingSavePlaceAtom =
      Atom(name: 'ReservesControllerBase.isLoadingSavePlace', context: context);

  @override
  bool get isLoadingSavePlace {
    _$isLoadingSavePlaceAtom.reportRead();
    return super.isLoadingSavePlace;
  }

  @override
  set isLoadingSavePlace(bool value) {
    _$isLoadingSavePlaceAtom.reportWrite(value, super.isLoadingSavePlace, () {
      super.isLoadingSavePlace = value;
    });
  }

  late final _$canRentAtom =
      Atom(name: 'ReservesControllerBase.canRent', context: context);

  @override
  int get canRent {
    _$canRentAtom.reportRead();
    return super.canRent;
  }

  @override
  set canRent(int value) {
    _$canRentAtom.reportWrite(value, super.canRent, () {
      super.canRent = value;
    });
  }

  late final _$durationAtom =
      Atom(name: 'ReservesControllerBase.duration', context: context);

  @override
  int get duration {
    _$durationAtom.reportRead();
    return super.duration;
  }

  @override
  set duration(int value) {
    _$durationAtom.reportWrite(value, super.duration, () {
      super.duration = value;
    });
  }

  late final _$file1Atom =
      Atom(name: 'ReservesControllerBase.file1', context: context);

  @override
  Map<String, dynamic> get file1 {
    _$file1Atom.reportRead();
    return super.file1;
  }

  @override
  set file1(Map<String, dynamic> value) {
    _$file1Atom.reportWrite(value, super.file1, () {
      super.file1 = value;
    });
  }

  late final _$file2Atom =
      Atom(name: 'ReservesControllerBase.file2', context: context);

  @override
  Map<String, dynamic> get file2 {
    _$file2Atom.reportRead();
    return super.file2;
  }

  @override
  set file2(Map<String, dynamic> value) {
    _$file2Atom.reportWrite(value, super.file2, () {
      super.file2 = value;
    });
  }

  late final _$imageAtom =
      Atom(name: 'ReservesControllerBase.image', context: context);

  @override
  Map<String, dynamic> get image {
    _$imageAtom.reportRead();
    return super.image;
  }

  @override
  set image(Map<String, dynamic> value) {
    _$imageAtom.reportWrite(value, super.image, () {
      super.image = value;
    });
  }

  late final _$placesEntitiesAtom =
      Atom(name: 'ReservesControllerBase.placesEntities', context: context);

  @override
  List<PlaceEntity> get placesEntities {
    _$placesEntitiesAtom.reportRead();
    return super.placesEntities;
  }

  @override
  set placesEntities(List<PlaceEntity> value) {
    _$placesEntitiesAtom.reportWrite(value, super.placesEntities, () {
      super.placesEntities = value;
    });
  }

  late final _$reservesAtom =
      Atom(name: 'ReservesControllerBase.reserves', context: context);

  @override
  List<ReserveEntity> get reserves {
    _$reservesAtom.reportRead();
    return super.reserves;
  }

  @override
  set reserves(List<ReserveEntity> value) {
    _$reservesAtom.reportWrite(value, super.reserves, () {
      super.reserves = value;
    });
  }

  late final _$selectedEventsAtom =
      Atom(name: 'ReservesControllerBase.selectedEvents', context: context);

  @override
  List<ReserveEntity> get selectedEvents {
    _$selectedEventsAtom.reportRead();
    return super.selectedEvents;
  }

  @override
  set selectedEvents(List<ReserveEntity> value) {
    _$selectedEventsAtom.reportWrite(value, super.selectedEvents, () {
      super.selectedEvents = value;
    });
  }

  late final _$selectedDayAtom =
      Atom(name: 'ReservesControllerBase.selectedDay', context: context);

  @override
  DateTime get selectedDay {
    _$selectedDayAtom.reportRead();
    return super.selectedDay;
  }

  @override
  set selectedDay(DateTime value) {
    _$selectedDayAtom.reportWrite(value, super.selectedDay, () {
      super.selectedDay = value;
    });
  }

  late final _$weekDaysAtom =
      Atom(name: 'ReservesControllerBase.weekDays', context: context);

  @override
  ObservableList<bool> get weekDays {
    _$weekDaysAtom.reportRead();
    return super.weekDays;
  }

  @override
  set weekDays(ObservableList<bool> value) {
    _$weekDaysAtom.reportWrite(value, super.weekDays, () {
      super.weekDays = value;
    });
  }

  late final _$eventsAtom =
      Atom(name: 'ReservesControllerBase.events', context: context);

  @override
  ObservableMap<DateTime, List<ReserveEntity>> get events {
    _$eventsAtom.reportRead();
    return super.events;
  }

  @override
  set events(ObservableMap<DateTime, List<ReserveEntity>> value) {
    _$eventsAtom.reportWrite(value, super.events, () {
      super.events = value;
    });
  }

  late final _$locationTermAtom =
      Atom(name: 'ReservesControllerBase.locationTerm', context: context);

  @override
  bool get locationTerm {
    _$locationTermAtom.reportRead();
    return super.locationTerm;
  }

  @override
  set locationTerm(bool value) {
    _$locationTermAtom.reportWrite(value, super.locationTerm, () {
      super.locationTerm = value;
    });
  }

  late final _$condOrUnitAtom =
      Atom(name: 'ReservesControllerBase.condOrUnit', context: context);

  @override
  int get condOrUnit {
    _$condOrUnitAtom.reportRead();
    return super.condOrUnit;
  }

  @override
  set condOrUnit(int value) {
    _$condOrUnitAtom.reportWrite(value, super.condOrUnit, () {
      super.condOrUnit = value;
    });
  }

  late final _$beginTimeAtom =
      Atom(name: 'ReservesControllerBase.beginTime', context: context);

  @override
  TimeOfDay get beginTime {
    _$beginTimeAtom.reportRead();
    return super.beginTime;
  }

  @override
  set beginTime(TimeOfDay value) {
    _$beginTimeAtom.reportWrite(value, super.beginTime, () {
      super.beginTime = value;
    });
  }

  late final _$endTimeAtom =
      Atom(name: 'ReservesControllerBase.endTime', context: context);

  @override
  TimeOfDay get endTime {
    _$endTimeAtom.reportRead();
    return super.endTime;
  }

  @override
  set endTime(TimeOfDay value) {
    _$endTimeAtom.reportWrite(value, super.endTime, () {
      super.endTime = value;
    });
  }

  late final _$beginTimeLimitAtom =
      Atom(name: 'ReservesControllerBase.beginTimeLimit', context: context);

  @override
  TimeOfDay get beginTimeLimit {
    _$beginTimeLimitAtom.reportRead();
    return super.beginTimeLimit;
  }

  @override
  set beginTimeLimit(TimeOfDay value) {
    _$beginTimeLimitAtom.reportWrite(value, super.beginTimeLimit, () {
      super.beginTimeLimit = value;
    });
  }

  late final _$endTimeLimitAtom =
      Atom(name: 'ReservesControllerBase.endTimeLimit', context: context);

  @override
  TimeOfDay get endTimeLimit {
    _$endTimeLimitAtom.reportRead();
    return super.endTimeLimit;
  }

  @override
  set endTimeLimit(TimeOfDay value) {
    _$endTimeLimitAtom.reportWrite(value, super.endTimeLimit, () {
      super.endTimeLimit = value;
    });
  }

  late final _$placeEntityAtom =
      Atom(name: 'ReservesControllerBase.placeEntity', context: context);

  @override
  PlaceEntity get placeEntity {
    _$placeEntityAtom.reportRead();
    return super.placeEntity;
  }

  @override
  set placeEntity(PlaceEntity value) {
    _$placeEntityAtom.reportWrite(value, super.placeEntity, () {
      super.placeEntity = value;
    });
  }

  late final _$reserveEntityAtom =
      Atom(name: 'ReservesControllerBase.reserveEntity', context: context);

  @override
  ReserveEntity get reserveEntity {
    _$reserveEntityAtom.reportRead();
    return super.reserveEntity;
  }

  @override
  set reserveEntity(ReserveEntity value) {
    _$reserveEntityAtom.reportWrite(value, super.reserveEntity, () {
      super.reserveEntity = value;
    });
  }

  late final _$selectedPlaceAtom =
      Atom(name: 'ReservesControllerBase.selectedPlace', context: context);

  @override
  String get selectedPlace {
    _$selectedPlaceAtom.reportRead();
    return super.selectedPlace;
  }

  @override
  set selectedPlace(String value) {
    _$selectedPlaceAtom.reportWrite(value, super.selectedPlace, () {
      super.selectedPlace = value;
    });
  }

  late final _$getPlacesAsyncAction =
      AsyncAction('ReservesControllerBase.getPlaces', context: context);

  @override
  Future<dynamic> getPlaces(String condId) {
    return _$getPlacesAsyncAction.run(() => super.getPlaces(condId));
  }

  late final _$getReservesAsyncAction =
      AsyncAction('ReservesControllerBase.getReserves', context: context);

  @override
  Future<dynamic> getReserves(String condId, DateTime date) {
    return _$getReservesAsyncAction.run(() => super.getReserves(condId, date));
  }

  late final _$getReservesByUnitAsyncAction =
      AsyncAction('ReservesControllerBase.getReservesByUnit', context: context);

  @override
  Future<dynamic> getReservesByUnit(
      String condId, DateTime date, String unitId) {
    return _$getReservesByUnitAsyncAction
        .run(() => super.getReservesByUnit(condId, date, unitId));
  }

  late final _$savePlaceAsyncAction =
      AsyncAction('ReservesControllerBase.savePlace', context: context);

  @override
  Future<dynamic> savePlace(String condId, List<String> weekdays) {
    return _$savePlaceAsyncAction.run(() => super.savePlace(condId, weekdays));
  }

  late final _$saveReserveAsyncAction =
      AsyncAction('ReservesControllerBase.saveReserve', context: context);

  @override
  Future<String> saveReserve(String condId,
      [String unitId = '',
      String unitName = '',
      String unitBlock = '',
      String userName = '']) {
    return _$saveReserveAsyncAction.run(
        () => super.saveReserve(condId, unitId, unitName, unitBlock, userName));
  }

  late final _$deleteReserveAsyncAction =
      AsyncAction('ReservesControllerBase.deleteReserve', context: context);

  @override
  Future deleteReserve(
      {required String condId,
      required DateTime date,
      required String reserveId}) {
    return _$deleteReserveAsyncAction.run(() =>
        super.deleteReserve(condId: condId, date: date, reserveId: reserveId));
  }

  late final _$deletePlaceAsyncAction =
      AsyncAction('ReservesControllerBase.deletePlace', context: context);

  @override
  Future<dynamic> deletePlace(String condId, String placeId) {
    return _$deletePlaceAsyncAction
        .run(() => super.deletePlace(condId, placeId));
  }

  late final _$ReservesControllerBaseActionController =
      ActionController(name: 'ReservesControllerBase', context: context);

  @override
  dynamic selectPlace(dynamic index) {
    final _$actionInfo = _$ReservesControllerBaseActionController.startAction(
        name: 'ReservesControllerBase.selectPlace');
    try {
      return super.selectPlace(index);
    } finally {
      _$ReservesControllerBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic increment() {
    final _$actionInfo = _$ReservesControllerBaseActionController.startAction(
        name: 'ReservesControllerBase.increment');
    try {
      return super.increment();
    } finally {
      _$ReservesControllerBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic decrement() {
    final _$actionInfo = _$ReservesControllerBaseActionController.startAction(
        name: 'ReservesControllerBase.decrement');
    try {
      return super.decrement();
    } finally {
      _$ReservesControllerBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void reset() {
    final _$actionInfo = _$ReservesControllerBaseActionController.startAction(
        name: 'ReservesControllerBase.reset');
    try {
      return super.reset();
    } finally {
      _$ReservesControllerBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic resetPlace() {
    final _$actionInfo = _$ReservesControllerBaseActionController.startAction(
        name: 'ReservesControllerBase.resetPlace');
    try {
      return super.resetPlace();
    } finally {
      _$ReservesControllerBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
deletedPlace: ${deletedPlace},
isLoading: ${isLoading},
isLoadingSavePlace: ${isLoadingSavePlace},
canRent: ${canRent},
duration: ${duration},
file1: ${file1},
file2: ${file2},
image: ${image},
placesEntities: ${placesEntities},
reserves: ${reserves},
selectedEvents: ${selectedEvents},
selectedDay: ${selectedDay},
weekDays: ${weekDays},
events: ${events},
locationTerm: ${locationTerm},
condOrUnit: ${condOrUnit},
beginTime: ${beginTime},
endTime: ${endTime},
beginTimeLimit: ${beginTimeLimit},
endTimeLimit: ${endTimeLimit},
placeEntity: ${placeEntity},
reserveEntity: ${reserveEntity},
selectedPlace: ${selectedPlace},
places: ${places},
stringDates: ${stringDates}
    ''';
  }
}
